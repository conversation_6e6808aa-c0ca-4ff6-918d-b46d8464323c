import { z } from "zod";

export type LoginFormValues = z.infer<typeof loginSchema>;

export const loginSchema = z.object({
  email: z
    .string({ required_error: "Correo requerido" })
    .email("Correo inválido"),
  password: z
    .string({ required_error: "Contraseña requerida" })
    .min(6, "Mínimo 6 caracteres"),
});

export type ReservationFormValues = z.infer<typeof reservationSchema>;

export const reservationSchema = z.object({
  amountOfPeople: z
    .string({ required_error: "Cantidad de personas requerida" })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 1, {
      message: "1 persona mínimo.",
    }),
  hoursRequested: z
    .string({ required_error: "Horas solicitadas requeridas" })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 1, {
      message: "1 hora mínimo.",
    }),
  selectedTimeRange: z
    .string({ required_error: "Selecciona un horario" })
    .min(1, "Selecciona un horario"),
});

export const confirmPasswordSchema = z
  .object({
    token: z.string({ required_error: "Token requerido" }),
    password: z
      .string({ required_error: "Contraseña requerida" })
      .min(6, "Mínimo 6 caracteres"),

    passwordConfirmation: z
      .string({ required_error: "Confirmación de contraseña requerida" })
      .min(6, "Mínimo 6 caracteres"),
  })
  .superRefine(({ password, passwordConfirmation }, ctx) => {
    if (password !== passwordConfirmation) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Las contraseñas no coinciden",
        path: ["passwordConfirmation"],
      });
    }
  });

export type ConfirmPasswordFormValues = z.infer<typeof confirmPasswordSchema>;

export const validateEmailSchema = z.object({
  email: z
    .string({ required_error: "Correo requerido" })
    .email("Correo inválido"),
});

export type ValidateEmailFormValues = z.infer<typeof validateEmailSchema>;

export const validateTokenSchema = z.object({
  token: z.string({ required_error: "Token requerido" }),
  email: z.string({ required_error: "Correo requerido" }),
});

export type ValidateTokenFormValues = z.infer<typeof validateTokenSchema>;
