import {
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  StyleSheet,
  KeyboardAvoidingView,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { FormField } from "../../components/forms/FormField";
import { theme } from "../../theme";
import { useZodForm } from "../../hooks/useZodForm";
import { LoginFormValues, loginSchema } from "../../schemas/schemas";
import { Button } from "../../components";
import { Feather } from "@expo/vector-icons";
import { useRoute } from "@react-navigation/native";
import { ConfirmPasswordRouteProp } from "../../navigation";

export const ConfirmPasswordScreen: React.FC = () => {
  const route = useRoute<ConfirmPasswordRouteProp>();
  const { token, email } = route.params;

  const { control, handleSubmit } = useZodForm(loginSchema);

  const onSubmit = (data: LoginFormValues) => {
    console.log("Submit data:", data);
    console.log(token);
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <LinearGradient
        colors={["#1B4959", "#DFD6C6"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 2.5, y: 0 }}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={styles.area}>
          <FormField
            control={control}
            name="password"
            placeholder="••••••••"
            secureTextEntry
            icon={
              <Feather
                name="lock"
                size={theme.fontSizes.md}
                color={theme.colors.gray700}
              />
            }
          />
          <FormField
            control={control}
            name="password"
            placeholder="••••••••"
            secureTextEntry
            icon={
              <Feather
                name="lock"
                size={theme.fontSizes.md}
                color={theme.colors.gray700}
              />
            }
          />
          <KeyboardAvoidingView>
            <Button onPress={handleSubmit(onSubmit)} title="Ingresar" />
          </KeyboardAvoidingView>
        </SafeAreaView>
      </LinearGradient>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingLeft: 20,
    paddingRight: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});
